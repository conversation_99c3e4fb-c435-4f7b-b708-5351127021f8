import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Linking,
  Alert,
  Dimensions,
  Image,
} from 'react-native';
import Modal from 'react-native-modal';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  runOnJS,
  withSpring
} from 'react-native-reanimated';
import { useTheme } from './ThemeContext';
import LinearGradient from 'react-native-linear-gradient';
import logger from '../services/productionLogger';

const { width, height } = Dimensions.get('window');

interface AccountSuspensionModalProps {
  visible: boolean;
  onClose: () => void;
  reason: string;
  minutesRemaining?: number;
  lockUntil?: string;
}

const AccountSuspensionModal: React.FC<AccountSuspensionModalProps> = ({
  visible,
  onClose,
  reason,
  minutesRemaining,
  lockUntil
}) => {
  const { theme, isDark } = useTheme();

  // Animation values for drag-to-dismiss
  const translateY = useSharedValue(0);
  const [isClosing, setIsClosing] = useState(false);

  // Reset animation when modal opens
  useEffect(() => {
    if (visible) {
      translateY.value = 0;
      setIsClosing(false);
    }
  }, [visible]);

  // Drag gesture handler
  const panGesture = Gesture.Pan()
    .onUpdate((event) => {
      // Only allow downward dragging
      if (event.translationY > 0) {
        translateY.value = event.translationY;
      }
    })
    .onEnd((event) => {
      const shouldClose = event.translationY > 80 || event.velocityY > 400;

      if (shouldClose) {
        translateY.value = withSpring(height, { damping: 20, stiffness: 300 });
        runOnJS(setIsClosing)(true);
        setTimeout(() => {
          runOnJS(onClose)();
        }, 200);
      } else {
        translateY.value = withSpring(0, { damping: 20, stiffness: 300 });
      }
    });

  // Animated style for the modal content
  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: translateY.value }],
    };
  });

  const handleContactSupport = async () => {
    try {
      logger.info('User contacted support from suspension modal', { reason, minutesRemaining }, 'support');
      
      // You can customize these contact methods based on your support channels
      const supportOptions = [
        {
          title: 'Email Support',
          action: () => {
            const email = '<EMAIL>'; // Replace with your support email
            const subject = 'Account Suspension - Need Assistance';
            const body = `Hello,\n\nMy account has been suspended with the following reason:\n"${reason}"\n\nPlease help me resolve this issue.\n\nThank you.`;
            const mailtoUrl = `mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
            
            Linking.openURL(mailtoUrl).catch(() => {
              Alert.alert('Error', 'Unable to open email app. <NAME_EMAIL> directly.');
            });
          }
        },
        {
          title: 'Call Support',
          action: () => {
            const phoneNumber = 'tel:+**********'; // Replace with your support phone number
            Linking.openURL(phoneNumber).catch(() => {
              Alert.alert('Error', 'Unable to make phone call. Please call +**************** directly.');
            });
          }
        },
        {
          title: 'Live Chat',
          action: () => {
            const chatUrl = 'https://vendy.com/support'; // Replace with your support chat URL
            Linking.openURL(chatUrl).catch(() => {
              Alert.alert('Error', 'Unable to open support chat. Please visit our website for assistance.');
            });
          }
        }
      ];

      Alert.alert(
        'Contact Support',
        'How would you like to contact our support team?',
        [
          ...supportOptions.map(option => ({
            text: option.title,
            onPress: option.action
          })),
          {
            text: 'Cancel',
            style: 'cancel'
          }
        ]
      );
    } catch (error) {
      logger.error('Error in contact support', error, 'support');
      Alert.alert('Error', 'Unable to contact support. Please try again later.');
    }
  };

  const formatTimeRemaining = () => {
    if (!minutesRemaining) return '';
    
    if (minutesRemaining < 60) {
      return `${minutesRemaining} minute${minutesRemaining !== 1 ? 's' : ''}`;
    } else {
      const hours = Math.floor(minutesRemaining / 60);
      const mins = minutesRemaining % 60;
      return `${hours} hour${hours !== 1 ? 's' : ''}${mins > 0 ? ` and ${mins} minute${mins !== 1 ? 's' : ''}` : ''}`;
    }
  };

  const styles = StyleSheet.create({
    modal: {
      justifyContent: 'flex-end',
      margin: 0,
    },
    modalContent: {
      backgroundColor: isDark ? '#1C1C1E' : '#FFFFFF',
      borderTopLeftRadius: 24,
      borderTopRightRadius: 24,
      paddingTop: 8,
      paddingHorizontal: 24,
      paddingBottom: 32,
      minHeight: height * 0.3,
      maxHeight: height * 0.45,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: -4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 8,
    },
    dragHandle: {
      width: 40,
      height: 4,
      backgroundColor: isDark ? '#48484A' : '#C7C7CC',
      borderRadius: 2,
      alignSelf: 'center',
      marginBottom: 20,
    },
    iconContainer: {
      width: 70,
      height: 70,
      borderRadius: 35,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 20,
      alignSelf: 'center',
    },
    iconImage: {
      width: 50,
      height: 50,
    },
    title: {
      fontSize: 22,
      fontWeight: 'bold',
      color: isDark ? '#FFFFFF' : '#000000',
      textAlign: 'center',
      marginBottom: 12,
    },
    infoText: {
      fontSize: 15,
      color: isDark ? '#8E8E93' : '#6D6D70',
      textAlign: 'center',
      marginBottom: 16,
      lineHeight: 20,
    },
    reasonContainer: {
      backgroundColor: isDark ? 'rgba(255, 69, 58, 0.1)' : 'rgba(255, 59, 48, 0.08)',
      borderRadius: 12,
      padding: 14,
      marginBottom: 16,
      width: '100%',
      borderWidth: 1,
      borderColor: isDark ? 'rgba(255, 69, 58, 0.2)' : 'rgba(255, 59, 48, 0.15)',
    },
    reasonLabel: {
      fontSize: 12,
      fontWeight: '600',
      color: isDark ? '#FF453A' : '#FF3B30',
      marginBottom: 6,
      textTransform: 'uppercase',
      letterSpacing: 0.8,
    },
    reasonText: {
      fontSize: 14,
      color: isDark ? '#FFFFFF' : '#000000',
      lineHeight: 18,
      fontWeight: '500',
    },
    timeContainer: {
      backgroundColor: isDark ? 'rgba(255, 69, 58, 0.12)' : 'rgba(255, 59, 48, 0.1)',
      borderRadius: 10,
      padding: 12,
      marginBottom: 20,
      width: '100%',
      borderWidth: 1,
      borderColor: isDark ? 'rgba(255, 69, 58, 0.25)' : 'rgba(255, 59, 48, 0.2)',
    },
    timeLabel: {
      fontSize: 12,
      fontWeight: '600',
      color: isDark ? '#FF453A' : '#FF3B30',
      textAlign: 'center',
      marginBottom: 3,
      textTransform: 'uppercase',
      letterSpacing: 0.5,
    },
    timeText: {
      fontSize: 16,
      fontWeight: 'bold',
      color: isDark ? '#FF453A' : '#FF3B30',
      textAlign: 'center',
    },
    buttonContainer: {
      width: '100%',
      gap: 12,
    },
    supportButton: {
      borderRadius: 14,
      borderWidth: 1.5,
      borderColor: isDark ? 'rgba(168, 85, 247, 0.5)' : 'rgba(139, 92, 246, 0.5)',
      backgroundColor: isDark ? 'rgba(168, 85, 247, 0.08)' : 'rgba(139, 92, 246, 0.08)',
      paddingVertical: 14,
      paddingHorizontal: 24,
      alignItems: 'center',
      marginTop: 4,
    },
    supportButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: isDark ? '#A855F7' : '#8B5CF6',
      letterSpacing: 0.3,
    },
    infoText: {
      fontSize: 14,
      color: isDark ? '#8E8E93' : '#6D6D70',
      textAlign: 'center',
      lineHeight: 20,
      marginBottom: 24,
    },
  });

  return (
    <Modal
      isVisible={visible && !isClosing}
      style={styles.modal}
      backdropColor="rgba(0, 0, 0, 0.5)"
      backdropOpacity={1}
      animationIn="slideInUp"
      animationOut="slideOutDown"
      animationInTiming={300}
      animationOutTiming={200}
      backdropTransitionInTiming={300}
      backdropTransitionOutTiming={200}
      useNativeDriverForBackdrop
      hideModalContentWhileAnimating
      onBackdropPress={onClose}
      onBackButtonPress={onClose}
      swipeDirection="down"
      onSwipeComplete={onClose}
      swipeThreshold={80}
    >
      <GestureDetector gesture={panGesture}>
        <Animated.View style={[styles.modalContent, animatedStyle]}>
          {/* Drag Handle */}
          <TouchableOpacity activeOpacity={1}>
            <View style={styles.dragHandle} />
          </TouchableOpacity>
        {/* Suspension Icon */}
        <View style={styles.iconContainer}>
          <Image
            source={require('../../assets/images/suspended.webp')}
            style={styles.iconImage}
            resizeMode="contain"
          />
        </View>

        {/* Title */}
        <Text style={styles.title}>Account Suspended</Text>

        {/* Info Text */}
        <Text style={styles.infoText}>
          Your account has been temporarily suspended for security reasons.
        </Text>

        {/* Reason Container */}
        <View style={styles.reasonContainer}>
          <Text style={styles.reasonLabel}>Suspension Reason</Text>
          <Text style={styles.reasonText}>{reason}</Text>
        </View>

        {/* Time Remaining (if available) */}
        {minutesRemaining && minutesRemaining > 0 && (
          <View style={styles.timeContainer}>
            <Text style={styles.timeLabel}>TIME REMAINING</Text>
            <Text style={styles.timeText}>{formatTimeRemaining()}</Text>
          </View>
        )}

        {/* Buttons */}
        <View style={styles.buttonContainer}>
          {/* Contact Support Button */}
          <TouchableOpacity
            style={styles.supportButton}
            onPress={handleContactSupport}
            activeOpacity={0.8}
          >
            <Text style={styles.supportButtonText}>Contact Support</Text>
          </TouchableOpacity>
        </View>
        </Animated.View>
      </GestureDetector>
    </Modal>
  );
};

export default AccountSuspensionModal;
