import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ImageBackground,
  Image,
  Animated,
  StyleSheet,
  Dimensions,
  Platform,
  Vibration,
  Alert,
} from 'react-native';
import { useTheme } from '../../components/ThemeContext';
import { useNavigation } from '@react-navigation/native';
import { FingerprintIcon, FaceIdIcon } from '../../components/icons';
import VendyLoader from '../../components/VendyLoader';
import { checkBiometricCapability, authenticateWithBiometric, getBiometricDisplayName } from '../../utils/biometricUtils';
import secureStorage from '../../services/secureStorageService';
import logger from '../../services/productionLogger';
import { PERFORMANCE_CONFIG } from '../../config/performance';
import ReactNativeHapticFeedback from 'react-native-haptic-feedback';
import { setupService } from '../../services/setupService';
import { apiService } from '../../services/apiService';
import AccountSuspensionModal from '../../components/AccountSuspensionModal';
import LinearGradient from 'react-native-linear-gradient';

const { width } = Dimensions.get('window');

interface ModernPinVerificationScreenProps {
  route?: {
    params?: {
      userData?: any;
      onSuccess?: () => void;
    };
  };
}

const ModernPinVerificationScreen: React.FC<ModernPinVerificationScreenProps> = ({ route }) => {
  const { theme, isDark } = useTheme();
  const navigation = useNavigation();

  const [pin, setPin] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPinInput, setShowPinInput] = useState(false);
  const [showBiometricOption, setShowBiometricOption] = useState(true); // Start with biometric screen
  const [userInfo, setUserInfo] = useState<{ firstName?: string; lastName?: string; email?: string } | null>(null);
  const [showSuspensionModal, setShowSuspensionModal] = useState(false);
  const [suspensionData, setSuspensionData] = useState<{ reason: string; minutesRemaining?: number; lockUntil?: string }>({
    reason: '',
    minutesRemaining: undefined,
    lockUntil: undefined,
  });
  const [forgotPinLoading, setForgotPinLoading] = useState(false);
  const errorTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Function to set error with auto-clear after 3 seconds
  const setErrorWithAutoClear = useCallback((errorMessage: string) => {
    // Clear any existing timeout
    if (errorTimeoutRef.current) {
      clearTimeout(errorTimeoutRef.current);
    }

    // Set the error message
    setError(errorMessage);

    // Auto-clear after 3 seconds
    errorTimeoutRef.current = setTimeout(() => {
      setError('');
      errorTimeoutRef.current = null;
    }, 3000);
  }, []);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (errorTimeoutRef.current) {
        clearTimeout(errorTimeoutRef.current);
      }
    };
  }, []);

  // Get user info from setup status API
  const displayName = useMemo(() => {
    if (userInfo?.firstName && userInfo?.lastName) {
      return `${userInfo.firstName} ${userInfo.lastName}`;
    } else if (userInfo?.firstName) {
      return userInfo.firstName;
    }
    return 'User'; // Fallback
  }, [userInfo]);

  const displayEmail = useMemo(() => {
    return userInfo?.email || '<EMAIL>';
  }, [userInfo]);

  const [biometricCapability, setBiometricCapability] = useState<any>({ available: false, type: 'fingerprint' });
    
  const shakeAnimation = useRef(new Animated.Value(0)).current;
  const fadeAnimation = useRef(new Animated.Value(0)).current;
  const biometricOpacity = useRef(new Animated.Value(1)).current;
  const pinInputOpacity = useRef(new Animated.Value(0)).current;
  const isMountedRef = useRef(true);

  // Initialize screen
  useEffect(() => {
    const initializeScreen = async () => {
      try {
        // Fade in animation
        Animated.timing(fadeAnimation, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }).start();

        // Fetch user data from setup status API
        try {
          logger.info('Fetching user data from setup status API', null, 'auth');
          const setupStatusResponse = await setupService.getSetupStatus();

          if (setupStatusResponse.user && isMountedRef.current) {
            setUserInfo({
              firstName: setupStatusResponse.user.firstName,
              lastName: setupStatusResponse.user.lastName,
              email: setupStatusResponse.user.email,
            });
            logger.info('User data loaded successfully', {
              firstName: setupStatusResponse.user.firstName,
              email: setupStatusResponse.user.email
            }, 'auth');
          }
        } catch (error) {
          logger.error('Failed to fetch user data from setup status', error, 'auth');
          // Continue with initialization even if user data fetch fails
        }

        // Check biometric capability
        const capability = await checkBiometricCapability();
        if (isMountedRef.current) {
          setBiometricCapability(capability);

          if (capability.available) {
            const biometricEnabled = await secureStorage.getBiometricToggle();
            logger.debug('Biometric capability check', {
              available: capability.available,
              type: capability.type,
              enabled: biometricEnabled
            }, 'auth');

            if (biometricEnabled) {
              setShowBiometricOption(true);
              setShowPinInput(false);
            } else {
              setShowBiometricOption(true); // Show biometric option even if not enabled
              setShowPinInput(false);
            }
          } else {
            // Always show biometric option first, even if not available
            setShowBiometricOption(true);
            setShowPinInput(false);
          }
        }
      } catch (error) {
        logger.error('Failed to initialize PIN verification screen', error, 'auth');
        if (isMountedRef.current) {
          setShowPinInput(true);
        }
      }
    };

    initializeScreen();
    return () => {
      isMountedRef.current = false;
    };
  }, []);



  // Handle keypad press with haptic feedback
  const handleKeypadPress = useCallback((number: string) => {
    if (loading || pin.length >= 4) return;

    // Enhanced haptic feedback for better UX
    if (PERFORMANCE_CONFIG.enableHaptics) {
      ReactNativeHapticFeedback.trigger('impactHeavy', {
        enableVibrateFallback: true,
        ignoreAndroidSystemSettings: false,
      });
    }

    const newPin = pin + number;
    setPin(newPin);

    // Clear error message when user starts typing new PIN
    if (error) {
      setError('');
    }

    // Auto-submit when 4 digits are entered
    if (newPin.length === 4) {
      setTimeout(() => handlePinSubmit(newPin), 100);
    }
  }, [pin, loading, error]);

  // Handle backspace
  const handleBackspace = useCallback(() => {
    if (loading) return;
    if (PERFORMANCE_CONFIG.enableHaptics) {
      ReactNativeHapticFeedback.trigger('impactMedium', {
        enableVibrateFallback: true,
        ignoreAndroidSystemSettings: false,
      });
    }
    setPin(prev => prev.slice(0, -1));
    setError('');
  }, [loading]);

  // Handle PIN submission
  const handlePinSubmit = useCallback(async (pinToSubmit?: string) => {
    const currentPin = pinToSubmit || pin;
    if (currentPin.length !== 4 || loading) return;

    setLoading(true);
    setError('');

    try {
      logger.info('Starting PIN verification', { pinLength: currentPin.length }, 'auth');

      // FIRST: Check account status before attempting PIN verification
      try {
        await apiService.checkAccountStatus();
        // If we get here, account is not locked
      } catch (statusError: any) {
        // Check if it's an account lockout error
        if (statusError.status === 423 || statusError.message?.includes('locked') || statusError.message?.includes('ACCOUNT_LOCKED')) {
          const errorData = statusError.response?.data?.data || {};
          const lockUntil = errorData.lockUntil;
          const minutesRemaining = errorData.minutesRemaining;

          // Show suspension modal immediately without trying PIN verification
          setSuspensionData({
            reason: 'Your account is temporarily suspended. PIN authentication is disabled.',
            minutesRemaining: minutesRemaining || undefined,
            lockUntil: lockUntil || undefined
          });
          setShowSuspensionModal(true);
          setLoading(false);
          return;
        }
        // If it's not a suspension error, continue with PIN verification (might be network issue)
      }

      // Use the actual setupService to verify PIN
      const result = await setupService.verifyPin({ pin: currentPin });

      if (result.pinVerified) {
        logger.info('PIN verification successful', { userId: result.user?.id }, 'auth');

        // Call success callback if provided
        route?.params?.onSuccess?.();

        // Navigate to main app
        navigation.navigate('MainTabs' as never);
      } else {
        throw new Error('PIN verification failed');
      }
    } catch (error: any) {
      logger.error('PIN verification failed', error, 'auth');

      // Check if it's an account lockout error
      if (error.status === 423 || error.message?.includes('locked') || error.message?.includes('ACCOUNT_LOCKED')) {
        logger.security('ACCOUNT_LOCKED_PIN_VERIFICATION', { error: error.message });

        // Extract lockout information
        const lockUntil = error.response?.data?.lockUntil || error.lockUntil;
        const minutesRemaining = lockUntil ? Math.ceil((new Date(lockUntil).getTime() - new Date().getTime()) / (60 * 1000)) : undefined;

        // Show suspension modal instead of error message
        setSuspensionData({
          reason: 'Too many failed PIN attempts. Your account has been temporarily locked for security.',
          minutesRemaining: minutesRemaining || undefined,
          lockUntil: lockUntil || undefined
        });
        setShowSuspensionModal(true);

        // Don't show error message or reset PIN for lockout
        return;
      }

      // Shake animation for error
      Animated.sequence([
        Animated.timing(shakeAnimation, { toValue: 10, duration: 100, useNativeDriver: true }),
        Animated.timing(shakeAnimation, { toValue: -10, duration: 100, useNativeDriver: true }),
        Animated.timing(shakeAnimation, { toValue: 10, duration: 100, useNativeDriver: true }),
        Animated.timing(shakeAnimation, { toValue: 0, duration: 100, useNativeDriver: true }),
      ]).start();

      // Simple error message with auto-clear after 3 seconds
      setErrorWithAutoClear('Incorrect PIN');
      setPin('');

      // Enhanced haptic feedback for errors - strongest feedback
      if (PERFORMANCE_CONFIG.enableHaptics) {
        ReactNativeHapticFeedback.trigger('notificationError', {
          enableVibrateFallback: true,
          ignoreAndroidSystemSettings: false,
        });
        // Add additional heavy impact for stronger feedback
        setTimeout(() => {
          ReactNativeHapticFeedback.trigger('impactHeavy', {
            enableVibrateFallback: true,
            ignoreAndroidSystemSettings: false,
          });
        }, 100);
      }
    } finally {
      setLoading(false);
    }
  }, [pin, loading, navigation, route, shakeAnimation]);

  // Handle biometric authentication
  const handleBiometricAuth = useCallback(async () => {
    if (loading || !biometricCapability.available) return;
    setLoading(true);
    try {
      // FIRST: Check account status before attempting biometric authentication
      try {
        await apiService.checkAccountStatus();
        // If we get here, account is not locked
      } catch (statusError: any) {
        // Check if it's an account lockout error
        if (statusError.status === 423 || statusError.message?.includes('locked') || statusError.message?.includes('ACCOUNT_LOCKED')) {
          const errorData = statusError.response?.data?.data || {};
          const lockUntil = errorData.lockUntil;
          const minutesRemaining = errorData.minutesRemaining;

          // Show suspension modal immediately without trying biometric authentication
          setSuspensionData({
            reason: 'Your account is temporarily suspended. Biometric authentication is disabled.',
            minutesRemaining: minutesRemaining || undefined,
            lockUntil: lockUntil || undefined
          });
          setShowSuspensionModal(true);
          setLoading(false);
          return;
        }
        // If it's not a suspension error, continue with biometric authentication (might be network issue)
      }

      const result = await authenticateWithBiometric(
        `Use ${getBiometricDisplayName(biometricCapability.type)} to access your account`,
        {
          cancelButtonText: 'Use PIN',
          fallbackButtonText: 'Use PIN instead',
          disableDeviceFallback: false
        }
      );

      if (result.success) {
        // CRITICAL: After successful biometric auth, verify account with backend
        try {
          // Call token check endpoint to verify account status
          await apiService.get('/auth/token-check');

          // If we get here, account is valid
        } catch (verificationError: any) {
          // Check if it's an account lockout error
          if (verificationError.status === 423 || verificationError.message?.includes('locked') || verificationError.message?.includes('ACCOUNT_LOCKED')) {
            const lockUntil = verificationError.response?.data?.lockUntil || verificationError.lockUntil;
            const minutesRemaining = lockUntil ? Math.ceil((new Date(lockUntil).getTime() - new Date().getTime()) / (60 * 1000)) : undefined;

            setSuspensionData({
              reason: 'Your account is suspended. Biometric authentication is disabled.',
              minutesRemaining: minutesRemaining || undefined,
              lockUntil: lockUntil || undefined
            });
            setShowSuspensionModal(true);
            setLoading(false);
            return;
          }
          logger.error('Account verification after biometric auth failed', verificationError);
          // Continue with authentication if verification fails due to network issues
        }

        // Double-check account status after successful biometric auth
        const finalStatusCheck = await setupService.getSetupStatus();
        if (finalStatusCheck.user?.isLocked || finalStatusCheck.user?.lockUntil) {
          const lockUntil = finalStatusCheck.user.lockUntil;
          const minutesRemaining = lockUntil ? Math.ceil((new Date(lockUntil).getTime() - new Date().getTime()) / (60 * 1000)) : undefined;

          setSuspensionData({
            reason: 'Your account was suspended during authentication. Access denied.',
            minutesRemaining: minutesRemaining || undefined,
            lockUntil: lockUntil || undefined
          });
          setShowSuspensionModal(true);
          setLoading(false);
          return;
        }

        route?.params?.onSuccess?.();
        navigation.navigate('MainTabs' as never);
      } else {
        // If biometric fails, show PIN input
        handleUsePinInstead();
      }
    } catch (error: any) {
      logger.error('Biometric authentication error', error);

      // Check if it's an account lockout error
      if (error.status === 423 || error.message?.includes('locked') || error.message?.includes('ACCOUNT_LOCKED')) {
        const lockUntil = error.response?.data?.lockUntil || error.lockUntil;
        const minutesRemaining = lockUntil ? Math.ceil((new Date(lockUntil).getTime() - new Date().getTime()) / (60 * 1000)) : undefined;

        setSuspensionData({
          reason: 'Your account is temporarily suspended. Biometric authentication is disabled.',
          minutesRemaining: minutesRemaining || undefined,
          lockUntil: lockUntil || undefined
        });
        setShowSuspensionModal(true);
        setLoading(false);
        return;
      }

      // If biometric fails, show PIN input
      handleUsePinInstead();
    } finally {
      setLoading(false);
    }
  }, [biometricCapability, loading, navigation, route]);

  // Handle switch to PIN input with smooth animation
  const handleUsePinInstead = useCallback(() => {
    if (PERFORMANCE_CONFIG.enableHaptics) {
      ReactNativeHapticFeedback.trigger('impactMedium', {
        enableVibrateFallback: true,
        ignoreAndroidSystemSettings: false,
      });
    }

    // Animate transition: fade out biometric, fade in PIN input
    Animated.parallel([
      Animated.timing(biometricOpacity, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(pinInputOpacity, {
        toValue: 1,
        duration: 200,
        delay: 100,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setShowBiometricOption(false);
      setShowPinInput(true);
    });
  }, [biometricOpacity, pinInputOpacity]);

  // Handle switch back to biometric screen
  const handleUseTouchIdInstead = useCallback(() => {
    if (PERFORMANCE_CONFIG.enableHaptics) {
      ReactNativeHapticFeedback.trigger('impactMedium', {
        enableVibrateFallback: true,
        ignoreAndroidSystemSettings: false,
      });
    }

    // Clear PIN and error
    setPin('');
    setError('');

    // Animate transition: fade out PIN input, fade in biometric
    Animated.parallel([
      Animated.timing(pinInputOpacity, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(biometricOpacity, {
        toValue: 1,
        duration: 200,
        delay: 100,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setShowPinInput(false);
      setShowBiometricOption(true);
    });
  }, [biometricOpacity, pinInputOpacity]);

  // Handle sign out
  const handleSignOut = useCallback(() => {
    navigation.navigate('Startup' as never);
  }, [navigation]);

  // Handle forgot PIN
  const handleForgotPin = useCallback(async () => {
    if (forgotPinLoading) return;

    setForgotPinLoading(true);

    try {
      // FIRST: Check account status before allowing forgot PIN
      try {
        await apiService.checkAccountStatus();
        // If we get here, account is not locked
      } catch (statusError: any) {
        // Check if it's an account lockout error
        if (statusError.status === 423 || statusError.message?.includes('locked') || statusError.message?.includes('ACCOUNT_LOCKED')) {
          const errorData = statusError.response?.data?.data || {};
          const lockUntil = errorData.lockUntil;
          const minutesRemaining = errorData.minutesRemaining;

          // Show suspension modal immediately
          setSuspensionData({
            reason: 'Your account is temporarily suspended. PIN reset is disabled.',
            minutesRemaining: minutesRemaining || undefined,
            lockUntil: lockUntil || undefined
          });
          setShowSuspensionModal(true);
          setForgotPinLoading(false);
          return;
        }
        // If it's not a suspension error, continue (might be network issue)
      }

      // Get user email from setup status
      const statusResponse = await setupService.getSetupStatus();
      const userEmail = statusResponse.user?.email;

      if (!userEmail) {
        throw new Error('Unable to retrieve your email address. Please contact support.');
      }

      // Send PIN reset OTP to user's email
      const response = await apiService.sendPinResetOTP(userEmail);

      if (response.data?.status === 'success') {
        logger.info('PIN reset OTP sent successfully', { email: userEmail }, 'auth');

        // Navigate to OTP verification screen
        (navigation as any).navigate('OtpVerification', {
          email: userEmail,
          userData: statusResponse.user
        });
      } else {
        throw new Error(response.data?.message || 'Failed to send reset code');
      }
    } catch (error: any) {
      logger.error('Forgot PIN error', error, 'auth');

      // Check if it's an account lockout error
      if (error.status === 423 || error.message?.includes('locked') || error.message?.includes('ACCOUNT_LOCKED')) {
        const lockUntil = error.response?.data?.lockUntil || error.lockUntil;
        const minutesRemaining = lockUntil ? Math.ceil((new Date(lockUntil).getTime() - new Date().getTime()) / (60 * 1000)) : undefined;

        setSuspensionData({
          reason: 'Your account is suspended. PIN reset is disabled.',
          minutesRemaining: minutesRemaining || undefined,
          lockUntil: lockUntil || undefined
        });
        setShowSuspensionModal(true);
      } else {
        Alert.alert(
          'Error',
          error.message || 'Failed to send reset code. Please try again.',
          [{ text: 'OK' }]
        );
      }
    } finally {
      setForgotPinLoading(false);
    }
  }, [forgotPinLoading, navigation, setupService, apiService]);

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: isDark ? '#0A0A0B' : '#FFFFFF',
    },
    backgroundOverlay: {
      ...StyleSheet.absoluteFillObject,
      backgroundColor: isDark ? 'rgba(10, 10, 11, 0.95)' : 'rgba(255, 255, 255, 0.95)',
    },
    content: {
      flex: 1,
      paddingHorizontal: 24,
    },
    // Header and Illustration
    illustration: {
      width: 80,
      height: 80,
      marginTop: 60,
      marginBottom: 24,
      alignSelf: 'center',
    },
    headerContainer: {
      alignItems: 'center',
      marginBottom: 40,
    },
    hiText: {
      fontSize: 18,
      fontWeight: '600',
      color: isDark ? '#FFFFFF' : '#1C1C1E',
      textAlign: 'center',
      marginBottom: 4,
    },
    emailRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: 4,
    },
    email: {
      fontSize: 14,
      color: isDark ? 'rgba(255, 255, 255, 0.6)' : 'rgba(60, 60, 67, 0.6)',
    },
    logout: {
      fontSize: 14,
      color: theme.colors.primary,
      marginLeft: 8,
      fontWeight: '500',
    },
    // Biometric Section (first screen)
    biometricContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: 32,
    },
    enterPinText: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.primary,
      textAlign: 'center',
      marginBottom: 60,
    },
    biometricIconContainer: {
      width: 120,
      height: 120,
      borderRadius: 60,
      backgroundColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 40,
      borderWidth: 2,
      borderColor: isDark ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.1)',
    },
    // PIN Input Section (second screen)
    pinContainer: {
      alignItems: 'center',
      marginBottom: 10,
      marginTop: -20,
    },
    pinInputLabel: {
      color: theme.colors.primary,
      fontSize: 16,
      fontWeight: '500',
      marginBottom: 20,
      textAlign: 'center',
    },
    pinInputContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      marginBottom: 12,
    },
    pinBox: {
      width: 56,
      height: 64,
      borderBottomWidth: 3,
      borderBottomColor: isDark ? 'rgba(255, 255, 255, 0.2)' : 'rgba(60, 60, 67, 0.2)',
      marginHorizontal: 10,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: isDark ? 'rgba(255, 255, 255, 0.05)' : 'rgba(118, 118, 128, 0.05)',
      borderRadius: 12,
      borderWidth: 1,
      borderColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(60, 60, 67, 0.1)',
      overflow: 'hidden',
    },
    pinBoxActive: {
      borderBottomColor: theme.colors.primary,
      borderColor: theme.colors.primary,
      shadowColor: theme.colors.primary,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.3,
      shadowRadius: 4,
      elevation: 3,
    },
    pinChar: {
      fontSize: 28,
      color: isDark ? '#FFFFFF' : '#1C1C1E',
      fontWeight: '700',
    },
    errorText: {
      color: '#FF3B30',
      fontSize: 15,
      textAlign: 'center',
      marginTop: 16,
      fontWeight: '500',
    },
    touchIdContainer: {
      alignItems: 'center',
      marginBottom: 20,
    },
    touchIdText: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.primary,
      textAlign: 'center',
    },
    // Keypad Container - ensures proper spacing
    keypadContainer: {
      position: 'absolute',
      bottom: 20,
      left: 0,
      right: 0,
      height: 350,
    },
    // Modern Keypad
    keypad: {
      width: '100%',
      alignSelf: 'center',
      paddingHorizontal: 0,
    },
    keypadRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 25,
      paddingHorizontal: 20,
    },
    key: {
      width: 80,
      height: 70,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'transparent',
      borderRadius: 35,
    },
    keyPressed: {
      backgroundColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
      borderRadius: 40,
      transform: [{ scale: 0.95 }],
    },
    keyText: {
      fontSize: 36,
      fontWeight: '400',
      color: isDark ? '#FFFFFF' : '#1C1C1E',
    },
    backspaceKey: {
      backgroundColor: 'transparent',
    },
    backspaceText: {
      fontSize: 32,
      fontWeight: '400',
      color: isDark ? 'rgba(255, 255, 255, 0.8)' : 'rgba(60, 60, 67, 0.8)',
    },
    emptyKey: {
      backgroundColor: 'transparent',
    },
    forgotPinButton: {
      position: 'absolute',
      bottom: 20,
      left: 24,
      paddingVertical: 8,
      paddingHorizontal: 12,
      borderRadius: 16,
      backgroundColor: isDark ? 'rgba(139, 92, 246, 0.08)' : 'rgba(139, 92, 246, 0.06)',
      borderWidth: 1,
      borderColor: isDark ? 'rgba(139, 92, 246, 0.25)' : 'rgba(139, 92, 246, 0.15)',
    },
    forgotPinText: {
      fontSize: 12,
      fontWeight: '500',
      color: isDark ? '#A855F7' : '#8B5CF6',
      letterSpacing: 0.2,
    },

  }), [theme, isDark, width]);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle={isDark ? "light-content" : "dark-content"}
        backgroundColor="transparent"
        translucent={true}
      />

      <ImageBackground
        source={isDark ? require("../../../assets/images/bg.jpeg") : require("../../../assets/images/bg-white.jpeg")}
        style={StyleSheet.absoluteFillObject}
        resizeMode="cover"
      />

      <View style={styles.backgroundOverlay} />

      <Animated.View style={[styles.content, { opacity: fadeAnimation }]}>
        {loading && (
          <View style={StyleSheet.absoluteFillObject}>
            <VendyLoader />
          </View>
        )}

        {/* Illustration at top */}
        <Image
          source={require('../../../assets/icons/vendy.png')}
          style={styles.illustration}
          resizeMode="contain"
        />

        {/* Header: Hi, Name and email/logout */}
        <View style={styles.headerContainer}>
          <Text style={styles.hiText}>Hi, {displayName}</Text>
          <View style={styles.emailRow}>
            <Text style={styles.email}>{displayEmail}</Text>
            <TouchableOpacity onPress={handleSignOut}>
              <Text style={styles.logout}>Logout</Text>
            </TouchableOpacity>
          </View>
        </View>


        {/* Biometric Screen (First Screen) */}
        {showBiometricOption && (
          <Animated.View style={[styles.biometricContainer, { opacity: biometricOpacity }]}>
            <TouchableOpacity onPress={handleUsePinInstead}>
              <Text style={styles.enterPinText}>Enter PIN</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.biometricIconContainer}
              onPress={handleBiometricAuth}
              disabled={loading}
            >
              {biometricCapability.type === 'face' ? (
                <FaceIdIcon size={60} color={theme.colors.primary} />
              ) : (
                <FingerprintIcon size={60} color={theme.colors.primary} />
              )}
            </TouchableOpacity>


          </Animated.View>
        )}

        {/* PIN Input Screen (Second Screen) */}
        {showPinInput && (
          <Animated.View style={[{ flex: 1 }, { opacity: pinInputOpacity }]}>
            <View style={styles.pinContainer}>
              <Text style={styles.pinInputLabel}>Verify 4-digit security PIN</Text>
              <Animated.View style={[styles.pinInputContainer, { transform: [{ translateX: shakeAnimation }] }]}>
                {[...Array(4)].map((_, i) => {
                  const isActive = pin.length > i;
                  return (
                    <View key={i} style={[styles.pinBox, isActive && styles.pinBoxActive]}>
                      {isActive && (
                        <LinearGradient
                          colors={['#8B5CF6', '#A855F7', '#FFFFFF']}
                          start={{ x: 0, y: 0 }}
                          end={{ x: 1, y: 1 }}
                          style={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0,
                            borderRadius: 12,
                            opacity: 0.1,
                          }}
                        />
                      )}
                      <Text style={styles.pinChar}>{pin[i] ? '•' : ''}</Text>
                    </View>
                  );
                })}
              </Animated.View>
              {error && <Text style={styles.errorText}>{error}</Text>}
            </View>

            {/* Use Touch ID link */}
            {biometricCapability.available && (
              <View style={styles.touchIdContainer}>
                <TouchableOpacity onPress={handleUseTouchIdInstead} disabled={loading}>
                  <Text style={styles.touchIdText}>Use Touch ID</Text>
                </TouchableOpacity>
              </View>
            )}

            {/* Modern Keypad - Fixed positioning */}
            <View style={styles.keypadContainer}>
              <View style={styles.keypad}>
                {[[1,2,3],[4,5,6],[7,8,9],["",0,"⌫"]].map((row, i) => (
                  <View key={i} style={styles.keypadRow}>
                    {row.map((item, j) => {
                      const isBackspace = item === '⌫';
                      const isEmpty = item === '';
                      const isZero = item === 0;
                      const isDisabled = isEmpty || (isBackspace && pin.length === 0);

                      return (
                        <TouchableOpacity
                          key={j}
                          style={[
                            styles.key,
                            isBackspace && styles.backspaceKey,
                            isEmpty && styles.emptyKey
                          ]}
                          onPress={() => {
                            if (isBackspace) handleBackspace();
                            else if (!isEmpty) handleKeypadPress(item.toString());
                          }}
                          disabled={isDisabled || loading}
                          activeOpacity={isEmpty ? 1 : 0.3}
                        >
                          {!isEmpty && (
                            <Text style={[
                              styles.keyText,
                              isBackspace && styles.backspaceText,
                              isZero && { fontWeight: '600', fontSize: 38 }
                            ]}>
                              {item}
                            </Text>
                          )}
                        </TouchableOpacity>
                      );
                    })}
                  </View>
                ))}
              </View>
            </View>


          </Animated.View>
        )}

        {/* Forgot PIN Button - Only visible in PIN input screen */}
        {showPinInput && (
          <TouchableOpacity
            style={styles.forgotPinButton}
            onPress={handleForgotPin}
            disabled={forgotPinLoading}
            activeOpacity={0.7}
          >
            <Text style={styles.forgotPinText}>
              {forgotPinLoading ? 'Sending...' : 'Forgot PIN?'}
            </Text>
          </TouchableOpacity>
        )}

      </Animated.View>

      {/* Account Suspension Modal */}
      <AccountSuspensionModal
        visible={showSuspensionModal}
        onClose={() => setShowSuspensionModal(false)}
        reason={suspensionData.reason}
        minutesRemaining={suspensionData.minutesRemaining}
        lockUntil={suspensionData.lockUntil}
      />
    </SafeAreaView>
  );
};

export default ModernPinVerificationScreen;