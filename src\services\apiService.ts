// Platform info now handled by appVersionService
import CryptoJ<PERSON> from 'crypto-js';
import secureStorage from './secureStorageService';
import crashReporting from './crashReportingService';
import performanceService from './performanceService';
import logger from './productionLogger';
import {
  ENV_CONFIG,
  PERFORMANCE_CONFIG,
  SECURITY_HEADERS,
  SIGNING_CONFIG,
  DEBUG_CONFIG,
  SSL_PINNING_CONFIG
} from '../config/environment';
import sslPinningService from './sslPinningService';
import nativeSSLPinningService from './nativeSSLPinning';
import appVersionService from '../utils/appVersion';

let NetInfo: any = null;
try {
  NetInfo = require('@react-native-community/netinfo').default;
} catch (error) {
  logger.warn('NetInfo module not available, network monitoring disabled', error, 'network');
}

interface RequestConfig {
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  cache?: boolean;
  cacheTime?: number;
  skipAuth?: boolean;
}

interface RequestOptions {
  method?: string;
  headers?: Record<string, string>;
  body?: string;
  signal?: AbortSignal;
}

interface ApiError extends Error {
  status?: number;
  response?: any;
}

interface CacheEntry {
  data: any;
  timestamp: number;
  expiresAt: number;
}

interface ApiResponse<T = any> {
  data: T;
  status: number;
}

class ApiService {
  private baseURL: string;
  private cache = new Map<string, CacheEntry>();
  private requestQueue: Array<() => Promise<any>> = [];
  private isOnline = true;
  private maxCacheSize = 100;
  private defaultTimeout = 30000; // 30 seconds (increased from 10 seconds)
  private defaultRetries = 3;
  private defaultRetryDelay = 1000; // 1 second
  private pendingRequests = new Map<string, Promise<any>>(); // Request deduplication
  private authToken: string | null = null; // Current authentication token

  constructor() {
    // Use environment-based URLs
    this.baseURL = this.getBaseURL();

    // Initialize SSL pinning
    this.initializeSSLPinning();

    // Initialize network monitoring
    this.initializeNetworkMonitoring();

    // Clean cache periodically
    this.startCacheCleanup();
  }

  private getBaseURL() {
    return ENV_CONFIG.API_BASE_URL;
  }

  private async initializeSSLPinning() {
    try {
      if (SSL_PINNING_CONFIG.enabled) {
        await sslPinningService.initialize();
        logger.info('SSL pinning service initialized', {
          service: 'ApiService',
          action: 'initializeSSLPinning',
          domains: SSL_PINNING_CONFIG.domains,
        });
      } else {
        logger.info('SSL pinning disabled', {
          service: 'ApiService',
          action: 'initializeSSLPinning',
          enabled: false,
        });
      }
    } catch (error) {
      logger.error('Failed to initialize SSL pinning', {
        service: 'ApiService',
        action: 'initializeSSLPinning',
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  private async initializeNetworkMonitoring() {
    try {
      // Check if NetInfo is available and properly initialized
      if (!NetInfo) {
        logger.info('NetInfo not available, using default online state', null, 'network');
        this.isOnline = true;
        return;
      }

      // Test if NetInfo methods are available
      if (typeof NetInfo.fetch !== 'function' || typeof NetInfo.addEventListener !== 'function') {
        logger.warn('NetInfo methods not available, using default online state', null, 'network');
        this.isOnline = true;
        return;
      }

      // Get initial network state with timeout
      const initialStatePromise = NetInfo.fetch();
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('NetInfo fetch timeout')), 5000)
      );

      const initialState = await Promise.race([initialStatePromise, timeoutPromise]);
      this.isOnline = initialState.isConnected ?? true;
      
      logger.info('Initial network state determined', {
        isConnected: initialState.isConnected,
        type: initialState.type,
        isInternetReachable: initialState.isInternetReachable
      }, 'network');

      // Monitor network connectivity
      const unsubscribe = NetInfo.addEventListener((state: any) => {
        const wasOnline = this.isOnline;
        this.isOnline = state.isConnected ?? true;
        
        logger.info('Network state changed', {
          isConnected: state.isConnected,
          type: state.type,
          isInternetReachable: state.isInternetReachable,
          wasOnline,
          isOnline: this.isOnline
        }, 'network');
        
        if (!wasOnline && this.isOnline) {
          // Back online - process queued requests
          this.processRequestQueue();
        }
        
        try {
          crashReporting.addBreadcrumb({
            category: 'network',
            message: `Network ${this.isOnline ? 'connected' : 'disconnected'}`,
            level: this.isOnline ? 'info' : 'warning',
            data: { connectionType: state.type, isConnected: state.isConnected },
          });
        } catch (breadcrumbError) {
          logger.warn('Failed to add network breadcrumb', breadcrumbError, 'network');
        }
      });
      
      return unsubscribe;
    } catch (error) {
      logger.warn('Network monitoring initialization failed', error, 'network');
      // Fallback to assuming online for production stability
      this.isOnline = true;
    }
  }

  private startCacheCleanup() {
    // Clean expired cache entries every 5 minutes
    setInterval(() => {
      this.cleanExpiredCache();
    }, 5 * 60 * 1000);
  }

  /**
   * Get stored access token - FAST VERSION (no biometric prompts)
   */
  async getAccessToken(): Promise<string | null> {
    try {
      // Use the quick access method to avoid biometric prompts
      return await secureStorage.getAccessToken();
    } catch (error) {
      logger.error('Error getting access token', error, 'auth');
      return null;
    }
  }

  /**
   * Get stored refresh token - FAST VERSION (no biometric prompts)
   */
  async getRefreshToken(): Promise<string | null> {
    try {
      // Use the quick access method to avoid biometric prompts
      return await secureStorage.getRefreshToken();
    } catch (error) {
      logger.error('Error getting refresh token', error, 'auth');
      return null;
    }
  }

  /**
   * Store tokens - SECURE VERSION
   */
  async storeTokens(accessToken: string, refreshToken: string): Promise<void> {
    try {
      if (accessToken && refreshToken) {
        const success = await secureStorage.storeAuthTokens(accessToken, refreshToken);
        if (!success) {
          throw new Error('Failed to store tokens securely');
        }
        logger.info('Tokens stored securely', null, 'auth');
      }
    } catch (error) {
      logger.error('Error storing tokens', error, 'auth');
      throw error; // Re-throw to let caller handle
    }
  }

  /**
   * Clear stored tokens - SECURE VERSION
   */
  async clearTokens(): Promise<void> {
    try {
      this.refreshAttempts = 0; // Reset refresh attempts when clearing tokens
      const success = await secureStorage.clearAuthData();
      if (success) {
        logger.info('Tokens cleared securely', null, 'auth');
      } else {
        logger.warn('Failed to clear some tokens', null, 'auth');
      }
    } catch (error) {
      logger.error('Error clearing tokens', error, 'auth');
    }
  }

  /**
   * Set the current authentication token for API requests
   */
  setAuthToken(token: string | null): void {
    this.authToken = token;
    if (token) {
      logger.debug('Auth token set for API requests', null, 'auth');
    } else {
      logger.debug('Auth token cleared from API requests', null, 'auth');
    }
  }

  /**
   * Get the current authentication token
   */
  getAuthToken(): string | null {
    return this.authToken;
  }

  // Cache management
  private getCacheKey(endpoint: string, options: any): string {
    const method = options.method || 'GET';
    const body = options.body || '';
    return `${method}:${endpoint}:${body}`;
  }

  private getFromCache(key: string): any | null {
    const entry = this.cache.get(key);
    if (!entry) return null;
    
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.data;
  }

  private setCache(key: string, data: any, cacheTime: number = 5 * 60 * 1000) {
    // Limit cache size
    if (this.cache.size >= this.maxCacheSize) {
      const firstKey = this.cache.keys().next().value;
      if (firstKey) {
        this.cache.delete(firstKey);
      }
    }
    
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      expiresAt: Date.now() + cacheTime,
    });
  }

  private cleanExpiredCache() {
    const now = Date.now();
    const entries = Array.from(this.cache.entries());
    for (const [key, entry] of entries) {
      if (now > entry.expiresAt) {
        this.cache.delete(key);
      }
    }
  }

  // Request queue for offline support
  private async processRequestQueue() {
    if (!this.isOnline || this.requestQueue.length === 0) return;
    
    logger.info(`Processing ${this.requestQueue.length} queued requests`, null, 'queue');
    
    const queue = [...this.requestQueue];
    this.requestQueue = [];
    
    for (const request of queue) {
      try {
        await request();
      } catch (error) {
        logger.error('Failed to process queued request', error, 'queue');
      }
    }
  }

  private async sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Enhanced request method with retry, caching, and error handling
  async request<T = any>(endpoint: string, options: RequestOptions = {}, config: RequestConfig = {}): Promise<ApiResponse<T>> {
    const {
      timeout = this.defaultTimeout,
      retries = this.defaultRetries,
      retryDelay = this.defaultRetryDelay,
      cache = false,
      cacheTime = 5 * 60 * 1000, // 5 minutes
      skipAuth = false,
    } = config;

    const method = options.method || 'GET';
    const url = `${this.baseURL}${endpoint}`;
    const cacheKey = this.getCacheKey(endpoint, options);

    // Validate SSL pinning for HTTPS requests
    if (SSL_PINNING_CONFIG.enabled && url.startsWith('https://')) {
      const isValidSSL = await sslPinningService.validateRequest(url);
      if (!isValidSSL) {
        const error = new Error('SSL certificate validation failed') as ApiError;
        error.status = 0;
        logger.error('SSL pinning validation failed', {
          service: 'ApiService',
          action: 'request',
          url,
          method,
        });
        throw error;
      }
    }

    // Check cache for GET requests
    if (method === 'GET' && cache) {
      const cachedData = this.getFromCache(cacheKey);
      if (cachedData) {
        logger.debug(`Cache hit for ${method} ${endpoint}`, null, 'cache');
        return cachedData;
      }
    }

    // Check if offline and queue request
    if (!this.isOnline && method !== 'GET') {
      return new Promise((resolve, reject) => {
        this.requestQueue.push(async () => {
          try {
            const result = await this.request(endpoint, options, config);
            resolve(result);
          } catch (error) {
            reject(error);
          }
        });
      });
    }

    // Start performance tracking
    const perfId = performanceService.startTiming(`api_${method}_${endpoint.replace(/\//g, '_')}`);
    const startTime = Date.now();

    let lastError: ApiError = new Error('Request failed') as ApiError;
    
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        // Get dynamic app version headers
        const versionHeaders = await appVersionService.getApiVersionHeaders();

        // Default headers
        const headers: Record<string, string> = {
          ...SECURITY_HEADERS,
          'X-Request-ID': `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          ...versionHeaders,
          ...options.headers,
        };

        // Add authorization header if token exists and not skipped
        if (!skipAuth) {
          // Use current auth token if set, otherwise get from storage
          const accessToken = this.authToken || await this.getAccessToken();
          if (accessToken) {
            headers.Authorization = `Bearer ${accessToken}`;
          }
        }

        logger.api(method, endpoint, 0, 0, { attempt: attempt + 1, maxAttempts: retries + 1 });

        // Create abort controller for timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        // Use native SSL pinning if available, otherwise fallback to regular fetch
        const response = nativeSSLPinningService.isAvailable()
          ? await nativeSSLPinningService.secureRequest(url, {
              ...options,
              headers,
              signal: controller.signal,
            })
          : await fetch(url, {
              ...options,
              headers,
              signal: controller.signal,
            });

        clearTimeout(timeoutId);

        let data;
        const contentType = response.headers.get('content-type');
        
        if (contentType && contentType.includes('application/json')) {
          data = await response.json();
        } else {
          data = await response.text();
        }

        const duration = Date.now() - startTime;

        // Track API performance
        performanceService.trackApiCall(method, endpoint, duration, response.status);
        performanceService.endTiming(perfId);

        // Add breadcrumb for API call
        crashReporting.recordApiCall(method, endpoint, response.status, duration);

        if (!response.ok) {
          // Handle token expiration/blacklisting
          if (response.status === 401 && !skipAuth) {
            // Log the full response for debugging
            logger.debug('🔍 [API] 401 response received', {
              endpoint,
              message: data.message,
              fullResponse: data
            }, 'auth');

            // Check if token is blacklisted/revoked - don't attempt refresh
            const errorMessage = data.message || '';
            const responseText = JSON.stringify(data).toLowerCase();

            if (errorMessage.toLowerCase().includes('blacklisted') ||
                errorMessage.toLowerCase().includes('revoked') ||
                errorMessage.toLowerCase().includes('token has been revoked') ||
                responseText.includes('blacklisted') ||
                responseText.includes('revoked')) {
              logger.warn('🚫 [API] Token is blacklisted/revoked - clearing tokens and throwing error', {
                message: errorMessage,
                endpoint
              }, 'auth');

              // Clear tokens immediately
              await this.clearTokens();

              // Throw error without attempting refresh
              const blacklistError: ApiError = new Error('Your session has been revoked. Please login again.') as ApiError;
              blacklistError.status = 401;
              blacklistError.response = data;
              throw blacklistError;
            }

            // Only attempt refresh for token-related errors
            if (errorMessage.toLowerCase().includes('token') ||
                errorMessage.toLowerCase().includes('expired') ||
                errorMessage.toLowerCase().includes('invalid')) {
              logger.info('Token expired, attempting refresh via TokenManager', null, 'auth');

              // Use centralized token manager
              const { tokenManager } = await import('./tokenManager');
              const refreshed = await tokenManager.refreshToken();

              if (refreshed) {
                // Retry the original request with new token
                logger.info('Token refreshed successfully, retrying request', null, 'auth');
                return this.request(endpoint, options, config);
              } else {
                // Refresh failed, session is invalid
                logger.warn('🚫 [API] Token refresh failed - clearing tokens', null, 'auth');
                await this.clearTokens();
                const sessionError: ApiError = new Error('Session expired. Please login again.') as ApiError;
                sessionError.status = 401;
                throw sessionError;
              }
            }
          }

          // Handle rate limiting
          if (response.status === 429) {
            const retryAfter = response.headers.get('Retry-After');
            const delay = retryAfter ? parseInt(retryAfter) * 1000 : retryDelay * Math.pow(2, attempt);
            
            if (attempt < retries) {
              logger.warn(`Rate limited, retrying after ${delay}ms`, { delay, attempt }, 'api');
              await this.sleep(delay);
              continue;
            }
          }

          // Handle server errors (5xx) with retry
          if (response.status >= 500 && attempt < retries) {
            const delay = retryDelay * Math.pow(2, attempt); // Exponential backoff
            logger.warn(`Server error ${response.status}, retrying after ${delay}ms`, { status: response.status, delay, attempt }, 'api');
            await this.sleep(delay);
            continue;
          }

          const error: ApiError = new Error(data.message || `HTTP ${response.status}`);
          error.status = response.status;
          error.response = data;
          throw error;
        }

        const result = { data, status: response.status };

        // Cache successful GET requests
        if (method === 'GET' && cache && response.status === 200) {
          this.setCache(cacheKey, result, cacheTime);
        }

        logger.api(method, endpoint, response.status, duration);
        return result;

      } catch (error) {
        // Convert unknown error to ApiError
        const apiError: ApiError = error instanceof Error 
          ? error as ApiError
          : new Error(String(error)) as ApiError;
        
        lastError = apiError;
        
        // Don't retry on certain errors
        if (
          apiError.name === 'AbortError' || // Timeout
          apiError.status === 400 || // Bad request
          apiError.status === 401 || // Unauthorized
          apiError.status === 403 || // Forbidden
          apiError.status === 404 || // Not found
          apiError.message.includes('JSON')  // JSON parse error
        ) {
          logger.api(method, endpoint, apiError.status || 500, Date.now() - startTime, { error: apiError.message });
          break;
        }
        
        // Retry for other errors if attempts remain
        if (attempt < retries) {
          const delay = retryDelay * Math.pow(2, attempt); // Exponential backoff
          logger.warn(`${method} ${endpoint} - Error, retrying in ${delay}ms`, { error: apiError.message, delay, attempt }, 'api');
          await this.sleep(delay);
          continue;
        }
        
        logger.error(`${method} ${endpoint} - Error after ${attempt + 1} attempts`, apiError, 'api');
      }
    }
    
    // If we got here, all attempts failed
    throw lastError;
  }

  // Track refresh attempts to prevent infinite loops
  private refreshAttempts = 0;
  private maxRefreshAttempts = 3;
  private refreshCooldown = 5000; // 5 seconds
  private lastRefreshAttempt = 0;

  // Refresh access token using enhanced auth session service
  async refreshToken(): Promise<boolean> {
    try {
      // Prevent infinite refresh loops
      const now = Date.now();
      if (now - this.lastRefreshAttempt < this.refreshCooldown) {
        logger.warn('Token refresh attempted too soon, cooling down', {
          timeSinceLastAttempt: now - this.lastRefreshAttempt,
          cooldownPeriod: this.refreshCooldown
        }, 'auth');
        return false;
      }

      if (this.refreshAttempts >= this.maxRefreshAttempts) {
        logger.error('Maximum token refresh attempts exceeded, clearing tokens', {
          attempts: this.refreshAttempts,
          maxAttempts: this.maxRefreshAttempts
        }, 'auth');
        await this.clearTokens();
        this.refreshAttempts = 0;
        return false;
      }

      this.lastRefreshAttempt = now;
      this.refreshAttempts++;

      const refreshToken = await this.getRefreshToken();
      if (!refreshToken) {
        logger.warn('No refresh token available for refresh', null, 'auth');
        this.refreshAttempts = 0;
        return false;
      }

      logger.info('Attempting to refresh token using auth session service', {
        attempt: this.refreshAttempts,
        maxAttempts: this.maxRefreshAttempts
      }, 'auth');

      // Use the enhanced auth session service for token refresh
      const { default: authSessionService } = await import('./authSessionService');
      const result = await authSessionService.refreshAccessToken(refreshToken);

      if (result.success && result.accessToken) {
        // Update the current auth token for immediate use
        this.setAuthToken(result.accessToken);
        logger.info('Token refreshed successfully via auth session service', null, 'auth');
        this.refreshAttempts = 0; // Reset attempts on success
        return true;
      }

      logger.warn('Token refresh failed via auth session service', {
        attempt: this.refreshAttempts,
        maxAttempts: this.maxRefreshAttempts
      }, 'auth');

      // If this was the last attempt, clear tokens
      if (this.refreshAttempts >= this.maxRefreshAttempts) {
        logger.error('All token refresh attempts failed, clearing tokens', null, 'auth');
        await this.clearTokens();
        this.refreshAttempts = 0;
      }

      return false;
    } catch (error) {
      logger.error('Error refreshing token', error, 'auth');
      this.refreshAttempts++;

      // If max attempts reached, clear tokens
      if (this.refreshAttempts >= this.maxRefreshAttempts) {
        logger.error('Token refresh error exceeded max attempts, clearing tokens', null, 'auth');
        await this.clearTokens();
        this.refreshAttempts = 0;
      }

      return false;
    }
  }

  // HTTP methods
  async get(endpoint: string, params: Record<string, any> = {}, config: RequestConfig = {}) {
    const queryString = new URLSearchParams(params).toString();
    const url = queryString ? `${endpoint}?${queryString}` : endpoint;

    // Enable caching by default for GET requests
    const getConfig = { cache: true, ...config };
    
    return this.request(url, { method: 'GET' }, getConfig);
  }

  async post(endpoint: string, data: any = {}, config: RequestConfig = {}) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    }, config);
  }

  async put(endpoint: string, data: any = {}, config: RequestConfig = {}) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    }, config);
  }

  async patch(endpoint: string, data: any = {}, config: RequestConfig = {}) {
    return this.request(endpoint, {
      method: 'PATCH',
      body: JSON.stringify(data),
    }, config);
  }

  async delete(endpoint: string, config: RequestConfig = {}) {
    return this.request(endpoint, { method: 'DELETE' }, config);
  }

  async uploadFile(endpoint: string, file: any, fieldName: string = 'file', additionalData: Record<string, any> = {}, config: RequestConfig = {}) {
    try {
      logger.info(`Uploading file to ${endpoint}`, null, 'upload');
      
      // Create form data
      const formData = new FormData();
      formData.append(fieldName, file);
      
      // Add additional data
      Object.keys(additionalData).forEach(key => {
        formData.append(key, additionalData[key]);
      });
      
      // Get access token if needed
      let headers: Record<string, string> = {};
      if (!config.skipAuth) {
        // Use current auth token if set, otherwise get from storage
        const accessToken = this.authToken || await this.getAccessToken();
        if (accessToken) {
          headers.Authorization = `Bearer ${accessToken}`;
        }
      }
      
      // Add dynamic app version headers
      const versionHeaders = await appVersionService.getApiVersionHeaders();
      headers = { ...headers, ...versionHeaders };
      
      // Start performance tracking
      const perfId = performanceService.startTiming(`api_upload_${endpoint.replace(/\//g, '_')}`);
      
      // Make request
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'POST',
        headers,
        body: formData,
      });
      
      // End performance tracking
      performanceService.endTiming(perfId);
      
      // Parse response
      let data;
      const contentType = response.headers.get('content-type');
      
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        data = await response.text();
      }
      
      if (!response.ok) {
        const error: ApiError = new Error(data.message || `HTTP ${response.status}`);
        error.status = response.status;
        error.response = data;
        throw error;
      }
      
      logger.info(`Upload ${endpoint} - Success`, null, 'upload');
      return { data, status: response.status };
      
    } catch (error) {
      // Convert unknown error to ApiError
      const apiError: ApiError = error instanceof Error 
        ? error as ApiError
        : new Error(String(error)) as ApiError;
      
      logger.error(`❌ [API] Upload ${endpoint} - Error:`, { error: apiError.message }, 'upload');
      throw apiError;
    }
  }

  // Authentication endpoints

  // Email endpoints
  async sendEmailOTP(email: string, purpose: string = 'verification'): Promise<any> {
    logger.info(`📧 [API] Sending email OTP to ${email} for ${purpose}`, null, 'email');
    return this.request('/email/send-otp', {
      method: 'POST',
      body: JSON.stringify({ email, purpose }),
    }, { skipAuth: true }); // Skip auth for public email OTP endpoint
  }

  async verifyEmailOTP(email: string, otp: string): Promise<any> {
    logger.info(`🔐 [API] Verifying email OTP for ${email}`, null, 'email');
    const response = await this.request('/email/verify-otp', {
      method: 'POST',
      body: JSON.stringify({ email, otp }),
    }, { skipAuth: true }); // Skip auth for public email OTP verification endpoint

    // Store tokens after successful verification
    if (response.data && response.data.tokens) {
      const { accessToken, refreshToken } = response.data.tokens;
      await this.storeTokens(accessToken, refreshToken);
    }

    return response;
  }

  // PIN Reset endpoints
  async sendPinResetOTP(email: string): Promise<any> {
    logger.info(`🔐 [API] Sending PIN reset OTP to ${email}`, null, 'pin_reset');
    return this.request('/auth/send-pin-reset-otp', {
      method: 'POST',
      body: JSON.stringify({ email }),
    }, {
      skipAuth: true,
      retries: 1, // Reduce retries for OTP sending
      timeout: 25000 // 15 second timeout
    });
  }

  async verifyPinResetOTP(email: string, otp: string): Promise<any> {
    logger.info(`🔐 [API] Verifying PIN reset OTP for ${email}`, null, 'pin_reset');
    return this.request('/auth/verify-pin-reset-otp', {
      method: 'POST',
      body: JSON.stringify({ email, otp }),
    }, {
      skipAuth: true,
      retries: 1, // Reduce retries for OTP verification
      timeout: 20000 // 10 second timeout
    });
  }

  async resetPin(resetToken: string, newPin: string): Promise<any> {
    const requestData = { resetToken, newPin };
    logger.info(`🔐 [API] Resetting PIN with token`, {
      hasResetToken: !!resetToken,
      resetTokenLength: resetToken?.length,
      hasPinData: !!newPin,
      requestData: { ...requestData, newPin: '****' } // Hide PIN in logs
    }, 'pin_reset');

    console.log('🔐 [API] Reset PIN request data:', {
      resetToken: resetToken ? `${resetToken.substring(0, 10)}...` : 'null',
      newPin: newPin ? '****' : 'null',
      fullPayload: JSON.stringify(requestData)
    });

    return this.request('/auth/reset-pin', {
      method: 'POST',
      body: JSON.stringify(requestData),
    }, {
      skipAuth: true,
      retries: 1, // Reduce retries for PIN reset
      timeout: 20000 // 20 second timeout
    });
  }


}

export const apiService = new ApiService();
export default apiService;

export const verifyEmailOtp = async (data: { email: string; otp: string }) => {
  try {
    const response = await apiService.post('/email/verify-otp', data, { skipAuth: true });

    if (response.data?.status === 'success') {
      // Store tokens if they exist in the response
      if (response.data.data?.tokens) {
        const { accessToken, refreshToken } = response.data.data.tokens;
        await apiService.storeTokens(accessToken, refreshToken);
      }
      return {
        success: true,
        data: response.data.data,
        message: response.data.message
      };
    }

    throw new Error(response.data?.message || 'Verification failed');
  } catch (error) {
    logger.error('❌ [API] Email verification error:', { error }, 'email');
    if (error && typeof error === 'object' && 'response' in error && error.response && typeof error.response === 'object' && 'data' in error.response && error.response.data && typeof error.response.data === 'object' && 'message' in error.response.data && typeof error.response.data.message === 'string') {
      throw new Error(error.response.data.message || 'Verification failed');
    }
    throw new Error('Verification failed');
  }
};
