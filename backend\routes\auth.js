const express = require('express');
const rateLimit = require('express-rate-limit');
const { body, param, validationResult } = require('express-validator');
const userService = require('../services/userService');
const smsService = require('../services/smsService');
const authService = require('../services/authService');
const otpService = require('../services/otpService');
const emailService = require('../services/brevoEmailService');
const logger = require('../utils/logger');

const router = express.Router();

// Rate limiting for auth endpoints
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 requests per windowMs
  message: {
    error: 'Too many authentication attempts, please try again later.',
    retryAfter: 900
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const otpLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 1, // limit each IP to 1 OTP request per minute
  message: {
    error: 'Please wait before requesting another OTP.',
    retryAfter: 60
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Validation middleware
const validatePhoneNumber = [
  body('phoneNumber')
    .matches(/^0[789][01]\d{8}$/)
    .withMessage('Please provide a valid Nigerian phone number (e.g., 08012345678)')
    .normalizeEmail()
];

const validateOTP = [
  body('otp')
    .isLength({ min: 6, max: 6 })
    .isNumeric()
    .withMessage('OTP must be a 6-digit number')
];

const validatePIN = [
  body('pin')
    .isLength({ min: 4, max: 6 })
    .isNumeric()
    .withMessage('PIN must be 4-6 digits')
];

/**
 * @route   GET /api/auth/test
 * @desc    Test endpoint to check if API is working
 * @access  Public
 */
router.get('/test', (req, res) => {
  console.log('🧪 [AUTH] Test endpoint called');
  console.log('🌐 Request IP:', req.ip);
  console.log('📋 Request headers:', JSON.stringify(req.headers, null, 2));

  res.status(200).json({
    status: 'success',
    message: 'Auth API is working!',
    timestamp: new Date().toISOString(),
    ip: req.ip
  });
});

/**
 * @route   GET /api/v1/auth/token-check
 * @desc    Check JWT token validity
 * @access  Private (requires valid token)
 */
router.get('/token-check', authService.protect.bind(authService), async (req, res) => {
  try {
    console.log('🔍 [AUTH] Token check endpoint called');
    console.log('👤 User ID:', req.user.id);

    // If we reach here, the token is valid (authService.protect middleware passed)
    const user = req.user;

    const responseData = {
      user: {
        id: user.id,
        isActive: user.is_active,
        role: user.role || 'user',
        isLocked: !!req.isLocked,
        lockUntil: user.lock_until || null,
        verifiedAt: new Date().toISOString()
      }
    };

    // CRITICAL: Handle locked accounts properly
    if (req.isLocked) {
      console.log('🔒 [AUTH] Token check for locked account:', user.id);
      return res.status(423).json({
        status: 'success', // Token is valid even if account is locked
        message: 'Token is valid but account is locked',
        code: 'ACCOUNT_LOCKED',
        data: responseData
      });
    }

    res.status(200).json({
      status: 'success',
      message: 'Token is valid',
      data: responseData
    });

  } catch (error) {
    console.log('❌ [AUTH] Token verification error:', error);
    res.status(401).json({
      status: 'error',
      message: 'Token verification failed'
    });
  }
});

/**
 * @route   POST /api/auth/send-pin-reset-otp
 * @desc    Send OTP for PIN reset to user's email
 * @access  Public
 */
router.post('/send-pin-reset-otp',
  [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Please provide a valid email address')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { email } = req.body;
      const clientIP = req.ip || req.connection.remoteAddress;

      console.log('🔐 [AUTH] PIN reset OTP request for email:', email);

      // Find user by email
      const user = await userService.findByEmail(email);
      if (!user) {
        console.log('❌ [AUTH] User not found for PIN reset');
        return res.status(404).json({
          status: 'error',
          message: 'No account found with this email address'
        });
      }

      // Check if user has a PIN to reset
      if (!user.pin) {
        console.log('❌ [AUTH] User has no PIN to reset');
        return res.status(400).json({
          status: 'error',
          message: 'No PIN found for this account'
        });
      }

      // Generate OTP for PIN reset
      const otp = await otpService.generateOTP(user.id, 'pin_reset');
      console.log('✅ [AUTH] PIN reset OTP generated:', otp);

      // Send OTP via email
      const emailResult = await emailService.sendOTP(email, otp, 'pin_reset');

      if (!emailResult.success) {
        console.log('❌ [AUTH] Failed to send PIN reset OTP email');
        return res.status(500).json({
          status: 'error',
          message: 'Failed to send reset code. Please try again.'
        });
      }

      console.log('✅ [AUTH] PIN reset OTP sent successfully');

      res.json({
        status: 'success',
        message: 'Reset code sent to your email',
        data: {
          email: email,
          expiresIn: 300 // 5 minutes
        }
      });

    } catch (error) {
      console.log('💥 [AUTH] PIN reset OTP error:', error);
      logger.error('PIN reset OTP error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @route   POST /api/auth/verify-pin-reset-otp
 * @desc    Verify OTP for PIN reset
 * @access  Public
 */
router.post('/verify-pin-reset-otp',
  [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Please provide a valid email address'),
    body('otp')
      .isLength({ min: 4, max: 4 })
      .isNumeric()
      .withMessage('OTP must be a 4-digit number')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { email, otp } = req.body;

      console.log('🔐 [AUTH] PIN reset OTP verification for email:', email);

      // Find user by email
      const user = await userService.findByEmail(email);
      if (!user) {
        console.log('❌ [AUTH] User not found for PIN reset verification');
        return res.status(404).json({
          status: 'error',
          message: 'Invalid request'
        });
      }

      // Verify OTP
      const isValidOTP = await otpService.verifyOTP(user.id, otp, 'pin_reset');
      if (!isValidOTP) {
        console.log('❌ [AUTH] Invalid PIN reset OTP');
        return res.status(400).json({
          status: 'error',
          message: 'Invalid or expired code'
        });
      }

      console.log('✅ [AUTH] PIN reset OTP verified successfully');

      // Generate temporary token for PIN reset
      console.log('🔐 [AUTH] About to generate PIN reset token for user:', user.id);
      const resetToken = await authService.generatePinResetToken(user.id);
      console.log('🔐 [AUTH] Generated reset token:', {
        hasToken: !!resetToken,
        tokenType: typeof resetToken,
        tokenLength: resetToken?.length,
        tokenPreview: resetToken ? `${resetToken.substring(0, 20)}...` : 'null'
      });

      const responseData = {
        resetToken: resetToken,
        userId: user.id
      };

      console.log('🔐 [AUTH] Sending response with data:', {
        hasResetToken: !!responseData.resetToken,
        userId: responseData.userId
      });

      res.json({
        status: 'success',
        message: 'Code verified successfully',
        data: responseData
      });

    } catch (error) {
      console.log('💥 [AUTH] PIN reset OTP verification error:', error);
      logger.error('PIN reset OTP verification error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @route   POST /api/auth/reset-pin
 * @desc    Reset user's PIN after OTP verification
 * @access  Public (with reset token)
 */
router.post('/reset-pin',
  [
    body('resetToken')
      .notEmpty()
      .withMessage('Reset token is required'),
    body('newPin')
      .isLength({ min: 4, max: 4 })
      .isNumeric()
      .withMessage('PIN must be a 4-digit number')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { resetToken, newPin } = req.body;

      console.log('🔐 [AUTH] PIN reset request with token');

      // Verify reset token
      const userId = await authService.verifyPinResetToken(resetToken);
      if (!userId) {
        console.log('❌ [AUTH] Invalid or expired reset token');
        return res.status(400).json({
          status: 'error',
          message: 'Invalid or expired reset token'
        });
      }

      // Get user to check current PIN
      const user = await userService.findById(userId);
      if (!user) {
        console.log('❌ [AUTH] User not found');
        return res.status(404).json({
          status: 'error',
          message: 'User not found'
        });
      }

      // Check if new PIN is the same as old PIN
      if (user.pin) {
        const bcrypt = require('bcryptjs');
        const isSamePin = await bcrypt.compare(newPin, user.pin);
        if (isSamePin) {
          console.log('❌ [AUTH] New PIN cannot be the same as old PIN');
          return res.status(400).json({
            status: 'error',
            message: 'New PIN cannot be the same as your current PIN. Please choose a different PIN.'
          });
        }
      }

      // Update user's PIN
      const success = await userService.updatePin(userId, newPin);
      if (!success) {
        console.log('❌ [AUTH] Failed to update PIN');
        return res.status(500).json({
          status: 'error',
          message: 'Failed to update PIN'
        });
      }

      console.log('✅ [AUTH] PIN reset successfully');

      res.json({
        status: 'success',
        message: 'PIN reset successfully'
      });

    } catch (error) {
      console.log('💥 [AUTH] PIN reset error:', error);
      logger.error('PIN reset error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @route   GET /api/auth/send-otp
 * @desc    Test endpoint for OTP (should use POST instead)
 * @access  Public
 */
router.get('/send-otp', (req, res) => {
  console.log('⚠️ [AUTH] GET request to send-otp endpoint');
  console.log('📱 Query params:', JSON.stringify(req.query, null, 2));

  res.status(405).json({
    status: 'error',
    message: 'Method not allowed. Please use POST request with phone number in body.',
    correctMethod: 'POST',
    correctBody: {
      phoneNumber: '08012345678'
    },
    example: 'curl -X POST http://localhost:3002/api/auth/send-otp -H "Content-Type: application/json" -d \'{"phoneNumber":"08012345678"}\''
  });
});

/**
 * @route   POST /api/v1/auth/send-otp
 * @desc    Send OTP to phone number for verification
 * @access  Public
 */
router.post('/send-otp',
  otpLimiter,
  validatePhoneNumber,
  async (req, res) => {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { phoneNumber } = req.body;
      const clientIP = req.ip || req.connection.remoteAddress;

      console.log('🚀 [AUTH] OTP Request Started');
      console.log('📱 Phone Number:', phoneNumber);
      console.log('🌐 Client IP:', clientIP);
      console.log('📝 Request Body:', JSON.stringify(req.body, null, 2));

      logger.info(`OTP request from ${clientIP} for phone: ${phoneNumber}`);

      // Check if user exists
      console.log('👤 [AUTH] Checking if user exists...');
      let user = await userService.findByPhoneNumber(phoneNumber);

      if (!user) {
        console.log('➕ [AUTH] User not found, creating new user...');
        // Create new user if doesn't exist
        user = await userService.createUser({
          phoneNumber
          // No PIN - new users will set it up during onboarding
        });
        console.log('✅ [AUTH] New user created:', user.id);

      } else {
        console.log('✅ [AUTH] Existing user found:', user.id);
      }

      // Check if user is blocked (permanent)
      if (user.isBlocked) {
        console.log('🚫 [AUTH] User account is permanently blocked');
        return res.status(403).json({
          status: 'error',
          message: 'Account has been blocked due to security concerns. Please contact support.',
          code: 'ACCOUNT_BLOCKED'
        });
      }

      // Check if user is locked (temporary)
      if (user.isLocked) {
        console.log('🔒 [AUTH] User account is temporarily locked');
        return res.status(423).json({
          status: 'error',
          message: 'Account is temporarily locked due to too many failed attempts. Please try again later.',
          code: 'ACCOUNT_LOCKED'
        });
      }

      // Check if additional verification is required
      if (user.requiresAdditionalVerification) {
        console.log('🔐 [AUTH] User requires additional verification');
        return res.status(428).json({
          status: 'error',
          message: 'Additional verification required. Please complete the verification process.',
          code: 'VERIFICATION_REQUIRED',
          verification_type: user.verificationType || 'enhanced'
        });
      }

      // Generate OTP
      console.log('🔢 [AUTH] Generating OTP...');
      const otp = await userService.createPhoneVerificationToken(user.id);
      console.log('✅ [AUTH] OTP generated:', otp);

      // Send OTP via SMS
      console.log('📨 [AUTH] Sending OTP via SMS...');
      console.log('📞 Target phone:', phoneNumber);
      console.log('🔢 OTP code:', otp);

      const smsResult = await smsService.sendOTP(phoneNumber, otp, 'verification');

      console.log('📨 [AUTH] SMS Result:', JSON.stringify(smsResult, null, 2));

      if (!smsResult.success) {
        console.log('❌ [AUTH] SMS sending failed:', smsResult.error);
        logger.error(`Failed to send OTP to ${phoneNumber}: ${smsResult.error}`);
        return res.status(500).json({
          status: 'error',
          message: 'Failed to send verification code. Please try again.'
        });
      }

      console.log('✅ [AUTH] SMS sent successfully!');
      console.log('📧 Message ID:', smsResult.messageId);

      logger.info(`OTP sent successfully to ${phoneNumber}. Message ID: ${smsResult.messageId}`);

      res.status(200).json({
        status: 'success',
        message: 'Verification code sent to your phone number',
        data: {
          phoneNumber,
          expiresIn: 300 // 5 minutes in seconds
        }
      });

    } catch (error) {
      console.log('💥 [AUTH] Unexpected error occurred:');
      console.log('❌ Error message:', error.message);
      console.log('📚 Error stack:', error.stack);
      console.log('🔍 Full error:', error);

      logger.error('Send OTP error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @route   POST /api/v1/auth/verify-otp
 * @desc    Verify OTP and create/login user
 * @access  Public
 */
router.post('/verify-otp',
  authLimiter,
  [...validatePhoneNumber, ...validateOTP],
  async (req, res) => {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { phoneNumber, otp } = req.body;
      const clientIP = req.ip || req.connection.remoteAddress;

      logger.info(`OTP verification attempt from ${clientIP} for phone: ${phoneNumber}`);

      // Find user
      const user = await userService.findByPhoneNumber(phoneNumber);

      if (!user) {
        return res.status(404).json({
          status: 'error',
          message: 'User not found. Please request a new verification code.'
        });
      }

      // Check if user is blocked (permanent)
      if (user.isBlocked) {
        return res.status(403).json({
          status: 'error',
          message: 'Account has been blocked due to security concerns. Please contact support.',
          code: 'ACCOUNT_BLOCKED'
        });
      }

      // Check if user is locked (temporary)
      if (user.isLocked) {
        return res.status(423).json({
          status: 'error',
          message: 'Account is temporarily locked. Please try again later.',
          code: 'ACCOUNT_LOCKED'
        });
      }

      // Check if additional verification is required
      if (user.requiresAdditionalVerification) {
        return res.status(428).json({
          status: 'error',
          message: 'Additional verification required. Please complete the verification process.',
          code: 'VERIFICATION_REQUIRED',
          verification_type: user.verificationType || 'enhanced'
        });
      }

      // Check if OTP token exists and hasn't expired
      if (!user.phoneVerificationToken || !user.phoneVerificationExpires) {
        await userService.incLoginAttempts(user.id);
        return res.status(400).json({
          status: 'error',
          message: 'No verification code found. Please request a new one.'
        });
      }

      if (new Date(user.phoneVerificationExpires) < new Date()) {
        await userService.incLoginAttempts(user.id);
        return res.status(400).json({
          status: 'error',
          message: 'Verification code has expired. Please request a new one.'
        });
      }

      // Verify OTP
      const isValidOTP = await userService.verifyPhoneToken(user.id, otp);

      if (!isValidOTP) {
        await userService.incLoginAttempts(user.id);
        logger.warn(`Invalid OTP attempt for ${phoneNumber} from ${clientIP}`);

        return res.status(400).json({
          status: 'error',
          message: 'Invalid verification code. Please try again.'
        });
      }

      // OTP is valid - add IP address and reset login attempts
      await userService.addIpAddress(user.id, clientIP);
      await userService.resetLoginAttempts(user.id);

      // Generate JWT tokens
      const tokens = await authService.generateTokens(user.id, {
        ipAddress: clientIP,
        userAgent: req.headers['user-agent']
      });



      logger.info(`Successful OTP verification for ${phoneNumber} from ${clientIP}`);

      res.status(200).json({
        status: 'success',
        message: 'Phone number verified successfully',
        data: {
          user: {
            id: user.id,
            phoneNumber: user.phoneNumber,
            isPhoneVerified: user.isPhoneVerified,
            balance: user.balance,
            createdAt: user.createdAt
          },
          tokens
        }
      });

    } catch (error) {
      logger.error('Verify OTP error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @route   POST /api/v1/auth/set-pin
 * @desc    Set transaction PIN for new users
 * @access  Private (requires valid JWT)
 */
router.post('/set-pin',
  authService.protect.bind(authService),
  validatePIN,
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { pin } = req.body;
      const userId = req.user.id;

      const user = await userService.findById(userId);
      if (!user) {
        return res.status(404).json({
          status: 'error',
          message: 'User not found'
        });
      }

      // Set the new PIN
      await userService.updateUser(userId, { pin });

      logger.info(`PIN set successfully for user ${userId}`);

      res.status(200).json({
        status: 'success',
        message: 'Transaction PIN set successfully'
      });

    } catch (error) {
      logger.error('Set PIN error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @route   POST /api/v1/auth/refresh-token
 * @desc    Refresh access token using refresh token
 * @access  Public
 */
router.post('/refresh-token', async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(400).json({
        status: 'error',
        message: 'Refresh token is required',
        code: 'REFRESH_TOKEN_REQUIRED'
      });
    }

    const tokens = await authService.refreshAccessToken(refreshToken);

    if (!tokens) {
      // Check if the refresh token is blacklisted for better error messaging
      const isBlacklisted = await authService.isTokenBlacklisted(refreshToken, 'refresh');

      if (isBlacklisted) {
        logger.warn('Refresh token is blacklisted/revoked', {
          tokenPreview: refreshToken.substring(0, 20) + '...'
        });
        return res.status(401).json({
          status: 'error',
          message: 'Refresh token has been revoked. Please login again.',
          code: 'REFRESH_TOKEN_REVOKED'
        });
      }

      return res.status(401).json({
        status: 'error',
        message: 'Invalid or expired refresh token',
        code: 'REFRESH_TOKEN_INVALID'
      });
    }

    logger.info('Token refresh successful', {
      userId: tokens.userId || 'unknown',
      tokenPreview: tokens.accessToken?.substring(0, 20) + '...'
    });

    res.status(200).json({
      status: 'success',
      message: 'Token refreshed successfully',
      data: {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        expiresIn: tokens.expiresIn || 604800 // 7 days default
      }
    });

  } catch (error) {
    logger.error('Refresh token error:', error);

    // Handle specific JWT errors
    if (error.message.includes('jwt expired')) {
      return res.status(401).json({
        status: 'error',
        message: 'Refresh token has expired. Please login again.',
        code: 'REFRESH_TOKEN_EXPIRED'
      });
    }

    if (error.message.includes('invalid token') || error.message.includes('jwt malformed')) {
      return res.status(401).json({
        status: 'error',
        message: 'Invalid refresh token format. Please login again.',
        code: 'REFRESH_TOKEN_MALFORMED'
      });
    }

    res.status(500).json({
      status: 'error',
      message: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
});

/**
 * @route   POST /api/v1/auth/logout
 * @desc    Logout user and blacklist tokens
 * @access  Private
 */
router.post('/logout', authService.protect.bind(authService), async (req, res) => {
  try {
    const { refreshToken } = req.body;
    const accessToken = req.token;

    await authService.logout(accessToken, refreshToken);

    logger.info(`User ${req.user.id} logged out successfully`);

    res.status(200).json({
      status: 'success',
      message: 'Logged out successfully'
    });

  } catch (error) {
    logger.error('Logout error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Logout failed'
    });
  }
});

/**
 * @route   POST /api/v1/auth/unlock-account
 * @desc    Manually unlock a user account (admin only)
 * @access  Private (Admin)
 */
router.post('/unlock-account',
  authService.protect,
  [
    body('userId')
      .isUUID()
      .withMessage('Valid user ID is required')
  ],
  async (req, res) => {
    try {
      // Check if requesting user is admin
      if (req.user.role !== 'admin') {
        return res.status(403).json({
          status: 'error',
          message: 'Admin access required'
        });
      }

      const { userId } = req.body;
      const adminId = req.user.id;

      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const success = await userService.unlockAccount(userId, adminId);

      if (success) {
        res.status(200).json({
          status: 'success',
          message: 'Account unlocked successfully'
        });
      } else {
        res.status(400).json({
          status: 'error',
          message: 'Failed to unlock account'
        });
      }

    } catch (error) {
      logger.error('Unlock account error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @route   GET /api/v1/auth/lock-status/:userId
 * @desc    Get account lock status (admin only)
 * @access  Private (Admin)
 */
router.get('/lock-status/:userId',
  authService.protect,
  [
    param('userId')
      .isUUID()
      .withMessage('Valid user ID is required')
  ],
  async (req, res) => {
    try {
      // Check if requesting user is admin
      if (req.user.role !== 'admin') {
        return res.status(403).json({
          status: 'error',
          message: 'Admin access required'
        });
      }

      const { userId } = req.params;

      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const lockStatus = await userService.getAccountLockStatus(userId);

      res.status(200).json({
        status: 'success',
        data: lockStatus
      });

    } catch (error) {
      logger.error('Get lock status error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @route   POST /api/v1/auth/clear-expired-locks
 * @desc    Clear all expired account locks (admin only)
 * @access  Private (Admin)
 */
router.post('/clear-expired-locks',
  authService.protect,
  async (req, res) => {
    try {
      // Check if requesting user is admin
      if (req.user.role !== 'admin') {
        return res.status(403).json({
          status: 'error',
          message: 'Admin access required'
        });
      }

      const unlockedCount = await userService.clearExpiredLocks();

      res.status(200).json({
        status: 'success',
        message: `Cleared expired locks for ${unlockedCount} accounts`,
        data: {
          unlockedCount
        }
      });

    } catch (error) {
      logger.error('Clear expired locks error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

module.exports = router;
